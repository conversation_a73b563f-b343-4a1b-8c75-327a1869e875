
-- 朝觐日程表 - 基础信息
DROP TABLE IF EXISTS `haji_jadwal`;
CREATE TABLE `haji_jadwal` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `item_no` INT COMMENT '项目编号',
    `time_info` TEXT COMMENT '手动输入的时间信息',
    `create_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间（毫秒时间戳）',
    `update_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间（毫秒时间戳）',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='朝觐时间表主表';

-- 朝觐日程表 - 多语言内容
DROP TABLE IF EXISTS `haji_jadwal_content`;
CREATE TABLE `haji_jadwal_content` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `jadwal_id` BIGINT UNSIGNED NOT NULL COMMENT '朝觐时间表ID，关联haji_jadwal.id',
    `language_id` TINYINT(3) UNSIGNED NOT NULL DEFAULT '0' COMMENT '语言ID: 0-中文, 1-英文, 2-印尼语',
    `event_summary` VARCHAR(255) NOT NULL COMMENT '事件简述',
    `additional_info` TEXT COMMENT '附加信息',
    `article_text` LONGTEXT COMMENT '文章详情（副文本）',
    `create_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间（毫秒时间戳）',
    `update_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间（毫秒时间戳）',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_jadwal_language` (`jadwal_id`, `language_id`) COMMENT '时间表ID和语言唯一索引',
    INDEX `idx_jadwal_id` (`jadwal_id`) COMMENT '时间表ID索引',
    INDEX `idx_language_id` (`language_id`) COMMENT '语言ID索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='朝觐时间表多语言内容表';

-- 朝觐日程配置表
DROP TABLE IF EXISTS `haji_jadwal_description`;
CREATE TABLE `haji_jadwal_description` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `year` INT NOT NULL COMMENT '朝觐年份',
    `language_id` TINYINT(3) UNSIGNED NOT NULL DEFAULT '0' COMMENT '语言ID: 0-中文, 1-英文, 2-印尼语',
    `description` TEXT NOT NULL COMMENT '日程说明文字',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_year_language` (`year`, `language_id`) COMMENT '年份和语言唯一索引',
    INDEX `idx_year` (`year`) COMMENT '年份索引',
    INDEX `idx_language_id` (`language_id`) COMMENT '语言ID索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='朝觐日程说明配置表';

-- 插入朝觐1446H日程数据
-- 插入主表数据
INSERT INTO `haji_jadwal` (`item_no`, `time_info`, `create_time`, `update_time`) VALUES
(1, '1 Mei 2025 (3 Dzulqa''dah 1446 H)', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(2, '2-16 Mei 2025 (4-18 Dzulqa''dah 1446 H)', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(3, '11 Mei-25 Mei 2025 (13-27 Dzulqa''dah 1446 H)', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(4, '17-31 Mei 2025 (19 Dzulqa''dah-4 Dzulhijjah 1446 H)', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(5, '31 Mei 2025 (4 Dzulhijjah 1446 H)', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(6, '4 Juni 2025 (8 Dzulhijjah 1446 H)', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(7, '5 Juni 2025 (9 Dzulhijjah 1446 H)', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(8, '6 Juni 2025 (10 Dzulhijjah 1446 H)', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(9, '7-9 Juni 2025 (11-13 Dzulhijjah 1446 H)', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(10, '11-25 Juni 2025 (15-29 Dzulhijjah 1446 H)', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(11, '18 Juni-2 Juli 2025 (15-29 Dzulhijjah 1446 H)', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(12, '26 Juni 2025 (1 Muharram 1447 H)', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(13, '26 Juni-10 Juli 2025 (1-15 Muharram 1447 H)', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(14, '11 Juli 2025 (17 Muharram 1447 H)', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000);

-- 插入中文内容数据
INSERT INTO `haji_jadwal_content` (`jadwal_id`, `language_id`, `event_summary`, `additional_info`, `create_time`, `update_time`) VALUES
(1, 0, 'Jamaah haji masuk Asrama Haji', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(2, 0, 'Jamaah haji Gelombang I berangkat dari Tanah Air ke Madinah', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(3, 0, 'Jamaah haji Gelombang I berangkat dari Madinah ke Makkah', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(4, 0, 'Jamaah haji Gelombang II berangkat dari Tanah Air ke Jeddah', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(5, 0, 'Closing date KAAIA pukul 24.00 WAS', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(6, 0, 'Jamaah haji berangkat dari Makkah ke Arafah', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(7, 0, 'Wukuf Arafah', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(8, 0, 'Idul Adha (Hari Raya Kurban)', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(9, 0, 'Hari Tasyriq I, Hari Tasyriq II (Nafar Awal), Hari Tasyriq III (Nafar Tsani)', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(10, 0, 'Pemulangan jamaah haji Gelombang I dari Jeddah ke Tanah Air', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(11, 0, 'Jamaah haji Gelombang II berangkat dari Makkah ke Madinah', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(12, 0, 'Tahun Baru Hijriah 1446 H', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(13, 0, 'Pemulangan jamaah haji Gelombang II dari Madinah ke Tanah Air', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(14, 0, 'Akhir kedatangan jamaah haji Gelombang II di Tanah Air', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),

-- 插入英文内容数据
(1, 1, 'Jamaah haji masuk Asrama Haji', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(2, 1, 'Jamaah haji Gelombang I berangkat dari Tanah Air ke Madinah', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(3, 1, 'Jamaah haji Gelombang I berangkat dari Madinah ke Makkah', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(4, 1, 'Jamaah haji Gelombang II berangkat dari Tanah Air ke Jeddah', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(5, 1, 'Closing date KAAIA pukul 24.00 WAS', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(6, 1, 'Jamaah haji berangkat dari Makkah ke Arafah', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(7, 1, 'Wukuf Arafah', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(8, 1, 'Idul Adha (Hari Raya Kurban)', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(9, 1, 'Hari Tasyriq I, Hari Tasyriq II (Nafar Awal), Hari Tasyriq III (Nafar Tsani)', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(10, 1, 'Pemulangan jamaah haji Gelombang I dari Jeddah ke Tanah Air', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(11, 1, 'Jamaah haji Gelombang II berangkat dari Makkah ke Madinah', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(12, 1, 'Tahun Baru Hijriah 1446 H', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(13, 1, 'Pemulangan jamaah haji Gelombang II dari Madinah ke Tanah Air', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(14, 1, 'Akhir kedatangan jamaah haji Gelombang II di Tanah Air', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),

-- 插入印尼语内容数据
(1, 2, 'Jamaah haji masuk Asrama Haji', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(2, 2, 'Jamaah haji Gelombang I berangkat dari Tanah Air ke Madinah', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(3, 2, 'Jamaah haji Gelombang I berangkat dari Madinah ke Makkah', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(4, 2, 'Jamaah haji Gelombang II berangkat dari Tanah Air ke Jeddah', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(5, 2, 'Closing date KAAIA pukul 24.00 WAS', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(6, 2, 'Jamaah haji berangkat dari Makkah ke Arafah', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(7, 2, 'Wukuf Arafah', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(8, 2, 'Idul Adha (Hari Raya Kurban)', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(9, 2, 'Hari Tasyriq I, Hari Tasyriq II (Nafar Awal), Hari Tasyriq III (Nafar Tsani)', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(10, 2, 'Pemulangan jamaah haji Gelombang I dari Jeddah ke Tanah Air', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(11, 2, 'Jamaah haji Gelombang II berangkat dari Makkah ke Madinah', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(12, 2, 'Tahun Baru Hijriah 1446 H', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(13, 2, 'Pemulangan jamaah haji Gelombang II dari Madinah ke Tanah Air', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(14, 2, 'Akhir kedatangan jamaah haji Gelombang II di Tanah Air', '', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000);

-- 插入朝觐日程说明
INSERT INTO `haji_jadwal_description` (`year`, `language_id`, `description`) VALUES
(2025, 0, 'Jadwal ini mengikuti Rencana Perjalanan Haji Kementerian Agama RI dengan penanggalan Hijriah yang mengacu pada Kalender Ummul Qura Arab Saudi. Waktu wukuf di Arafah ditetapkan setelah rukyat hilal awal Dzulhijjah oleh Kerajaan Arab Saudi.');
(2025, 1, 'Jadwal ini mengikuti Rencana Perjalanan Haji Kementerian Agama RI dengan penanggalan Hijriah yang mengacu pada Kalender Ummul Qura Arab Saudi. Waktu wukuf di Arafah ditetapkan setelah rukyat hilal awal Dzulhijjah oleh Kerajaan Arab Saudi.');
(2025, 2, 'Jadwal ini mengikuti Rencana Perjalanan Haji Kementerian Agama RI dengan penanggalan Hijriah yang mengacu pada Kalender Ummul Qura Arab Saudi. Waktu wukuf di Arafah ditetapkan setelah rukyat hilal awal Dzulhijjah oleh Kerajaan Arab Saudi.');

